from django.shortcuts import render, get_object_or_404
from django.core.paginator import Paginator
from django.db.models import Q
import json

from .models import Site, SiteMap, ColumnRule, Content
from .model.column import Column
from .enums import WebsiteContentType
from apps.website.service.column_rule_manager import ColumnRuleManager
from apps.website.service.column_service import ColumnService
from apps.hyqm.models import MediaAccount
from apps.hyqm.enums import PlatformType


def get_site_initialization_status(site):
    """
    检查站点初始化状态
    
    Returns:
        dict: {
            'is_initialized': bool,
            'status': str,  # 'not_initialized', 'partial', 'completed'
            'status_display': str,
            'sitemap_count': int,
            'content_classified_count': int,
            'total_content_count': int,
            'suggestions': list
        }
    """
    sitemap_count = SiteMap.objects.filter(site_id=site.id).count()
    
    # 如果没有sitemap，说明完全未初始化
    if sitemap_count == 0:
        return {
            'is_initialized': False,
            'status': 'not_initialized',
            'status_display': '未初始化',
            'sitemap_count': 0,
            'content_classified_count': 0,
            'total_content_count': 0,
            'suggestions': ['执行站点初始化，抓取网站地图']
        }
    
    # 检查内容类型分类情况
    total_content_count = SiteMap.objects.filter(site_id=site.id).count()
    content_classified_count = SiteMap.objects.filter(
        site_id=site.id
    ).exclude(content_type='unknown').count()
    
    unknown_count = total_content_count - content_classified_count
    
    suggestions = []
    
    # 判断初始化状态
    if unknown_count > 0:
        if content_classified_count == 0:
            status = 'not_initialized'
            status_display = '未分类'
            suggestions.append('执行AI内容分类')
        else:
            status = 'partial'
            status_display = '部分分类'
            suggestions.append(f'还有{unknown_count}个URL未分类')
    else:
        status = 'completed'
        status_display = '已完成'
        
        # 检查是否有内容类型但没有具体内容
        content_urls = SiteMap.objects.filter(
            site_id=site.id,
            content_type='content'
        ).count()
        
        if content_urls > 0:
            content_records = Content.objects.filter(
                sitemap__site_id=site.id
            ).count()
            
            if content_records < content_urls:
                suggestions.append(f'有{content_urls - content_records}个内容页面待抓取')
    
    return {
        'is_initialized': status == 'completed',
        'status': status,
        'status_display': status_display,
        'sitemap_count': sitemap_count,
        'content_classified_count': content_classified_count,
        'total_content_count': total_content_count,
        'suggestions': suggestions
    }


def site_list(request):
    """
    http://127.0.0.1:8000/website/sites/
    站点列表
    """
    # 获取搜索参数
    search_query = request.GET.get('search', '').strip()
    domain_filter = request.GET.get('domain', '').strip()
    status_filter = request.GET.get('status', '').strip()
    
    # 构建查询，显示所有站点（包括子站点）
    sites = Site.objects.all()
    
    # 应用搜索过滤
    if search_query:
        sites = sites.filter(
            Q(name__icontains=search_query) |
            Q(domain__icontains=search_query) |
            Q(description__icontains=search_query)
        )
    
    if domain_filter:
        sites = sites.filter(domain__icontains=domain_filter)
    
    # 应用状态过滤
    if status_filter:
        sites = sites.filter(status=status_filter)
    
    # 排序
    sites = sites.order_by('name')
    
    # 分页处理
    paginator = Paginator(sites, 20)
    page = request.GET.get('page', 1)
    sites_page = paginator.get_page(page)
    
    # 计算统计信息和初始化状态
    sitemap_counts = {}
    initialization_status = {}
    media_account_status = {}
    
    for site in sites_page:
        sitemap_counts[site.id] = SiteMap.objects.filter(site_id=site.id).count()
        initialization_status[site.id] = get_site_initialization_status(site)
        
        # 检查MediaAccount同步状态
        existing_media_account = MediaAccount.objects.filter(
            account_id=site.domain,
            platform_type=PlatformType.WEBSITE,
            delete_time__isnull=True
        ).first()
        
        media_account_status[site.id] = {
            'is_synced': existing_media_account is not None,
            'media_account_id': existing_media_account.id if existing_media_account else None
        }
    
    return render(request, 'site_list.html', {
        'sites': sites_page,
        'sitemap_counts': sitemap_counts,
        'initialization_status': initialization_status,
        'media_account_status': media_account_status,
        'search_query': search_query,
        'domain_filter': domain_filter,
        'status_filter': status_filter,
        'status_choices': Site.STATUS_CHOICES,
        'total_sites': sites.count()
    })


def site_detail(request, site_id):
    """站点详情"""
    site = get_object_or_404(Site, id=site_id)
    
    # 获取筛选参数
    content_type = request.GET.get('content_type', '')
    search_query = request.GET.get('search', '').strip()
    
    # 构建查询，显示所有类型的页面
    sitemaps = SiteMap.objects.filter(site_id=site_id)
    
    # 应用筛选
    if content_type:
        sitemaps = sitemaps.filter(content_type=content_type)
    
    if search_query:
        sitemaps = sitemaps.filter(
            Q(url__icontains=search_query) |
            Q(title__icontains=search_query)
        )
    
    # 按内容类型统计（统计所有类型，不受过滤影响）
    content_type_stats = {}
    for choice in WebsiteContentType.choices:
        count = SiteMap.objects.filter(
            site_id=site_id,
            content_type=choice[0]
        ).count()
        if count > 0:
            content_type_stats[choice[0]] = {
                'label': choice[1],
                'count': count
            }
    
    # 排序：未知类型(unknown)和空类型靠前，其他类型靠后
    from django.db.models import Case, When, Value, CharField
    
    sitemaps = sitemaps.annotate(
        content_type_priority=Case(
            When(content_type__isnull=True, then=Value('0_null')),
            When(content_type='', then=Value('0_empty')), 
            When(content_type='unknown', then=Value('1_unknown')),
            default=Value('2_other'),
            output_field=CharField(),
        )
    ).order_by('content_type_priority', 'url')
    paginator = Paginator(sitemaps, 50)
    page = request.GET.get('page', 1)
    sitemaps_page = paginator.get_page(page)
    
    return render(request, 'site_detail.html', {
        'site': site,
        'sitemaps': sitemaps_page,
        'content_type': content_type,
        'search_query': search_query,
        'content_type_stats': content_type_stats,
        'content_type_choices': WebsiteContentType.choices,
        'content_type_choices_json': json.dumps(list(WebsiteContentType.choices)),
        'total_sitemaps': sitemaps.count()
    })


def sitemap_list(request):
    """网站地图列表"""
    site_id = request.GET.get('site_id')
    content_type = request.GET.get('content_type')
    search_query = request.GET.get('search', '').strip()
    
    # 构建查询
    sitemaps = SiteMap.objects.select_related('site').all()
    
    # 应用筛选
    if site_id:
        sitemaps = sitemaps.filter(site_id=site_id)
    
    if content_type:
        sitemaps = sitemaps.filter(content_type=content_type)
    
    if search_query:
        sitemaps = sitemaps.filter(
            Q(url__icontains=search_query) |
            Q(title__icontains=search_query) |
            Q(site__name__icontains=search_query)
        )
    
    # 排序
    sitemaps = sitemaps.order_by('site__name', 'url')
    
    # 分页
    paginator = Paginator(sitemaps, 50)
    page = request.GET.get('page', 1)
    sitemaps_page = paginator.get_page(page)
    
    # 获取所有站点用于筛选
    sites = Site.objects.all().order_by('name')
    
    return render(request, 'sitemap_list.html', {
        'sitemaps': sitemaps_page,
        'sites': sites,
        'site_id': site_id,
        'content_type': content_type,
        'search_query': search_query,
        'content_type_choices': WebsiteContentType.choices,
        'total_sitemaps': sitemaps.count()
    })


def sitemap_detail(request, sitemap_id):
    """SiteMap详情页面"""
    sitemap = get_object_or_404(SiteMap, id=sitemap_id)
    
    # 获取关联的规则信息
    column_rule = ColumnRuleManager.get_column_rule(sitemap)
    
    # 从Content表获取内容提取规则
    content = sitemap.get_content()
    content_extraction_rule = content.get_content_extraction_rule() if content else None

    return render(request, 'sitemap_detail.html', {
        'sitemap': sitemap,
        'column_rule': column_rule,
        'content_extraction_rule': content_extraction_rule,
        'content_type': sitemap.get_content_type()
    })


def column_rules_list(request, site_id):
    """栏目规则列表页面"""
    site = get_object_or_404(Site, id=site_id)
    
    # 获取该站点的所有栏目规则
    column_rules = ColumnRule.objects.filter(site_id=site_id).order_by('-created_at')
    
    # 分页处理
    paginator = Paginator(column_rules, 20)
    page = request.GET.get('page', 1)
    rules_page = paginator.get_page(page)
    
    return render(request, 'column_rules_list.html', {
        'site': site,
        'column_rules': rules_page,
        'total_rules': column_rules.count()
    })


def site_columns_list(request, site_id):
    """站点栏目列表页面"""
    site = get_object_or_404(Site, id=site_id)
    
    # 获取该站点下所有内容栏目页类型的SiteMap
    content_section_sitemaps = SiteMap.objects.filter(
        site_id=site_id,
        content_type='content_section'
    ).order_by('url')
    
    # 检查每个栏目是否已匹配规则（只查询，不自动生成）
    columns_data = []
    column_service = ColumnService()
    
    for sitemap in content_section_sitemaps:
        column_rule = ColumnRuleManager.get_column_rule_without_create(sitemap)
        
        # 获取或创建Column对象以获取统计信息
        column = None
        content_count = 0
        page_count = 0
        
        if column_rule is not None:
            # 如果有规则，获取Column对象
            column = column_service.get_or_create_column(sitemap)
            if column:
                content_count = column.get_content_count()
                # 页面数量可以基于已抓取页码计算
                page_count = len(column.crawled_pages) if column.crawled_pages else 0
        
        columns_data.append({
            'sitemap': sitemap,
            'has_rule': column_rule is not None,
            'column_rule': column_rule,
            'column_id': column.id if column else None,
            'content_count': content_count,
            'page_count': page_count
        })
    
    # 分页处理
    paginator = Paginator(columns_data, 20)
    page = request.GET.get('page', 1)
    columns_page = paginator.get_page(page)
    
    return render(request, 'site_columns_list.html', {
        'site': site,
        'columns_data': columns_page,
        'total_columns': len(columns_data)
    })


def column_content_list(request, site_id, column_id):
    """栏目内容列表页面"""
    site = get_object_or_404(Site, id=site_id)
    column = get_object_or_404(Column, id=column_id, site_id=site_id)
    
    # 获取搜索参数
    search_query = request.GET.get('search', '').strip()
    status_filter = request.GET.get('status', '').strip()
    
    # 获取该栏目下的所有Content记录
    contents = Content.objects.filter(column_id=column_id)
    
    # 应用搜索筛选
    if search_query:
        contents = contents.filter(
            Q(title__icontains=search_query) |
            Q(url__icontains=search_query)
        )
    
    # 应用状态筛选
    if status_filter:
        contents = contents.filter(status=status_filter)
    
    # 获取状态统计
    from .enums import ContentStatus
    status_choices = [
        (ContentStatus.PENDING, '待提取'),
        (ContentStatus.EXTRACTED, '已提取'),
        (ContentStatus.FAILED, '提取失败'),
        (ContentStatus.MANUAL, '人工处理'),
        (ContentStatus.NO_RULE_MATCH, '无法匹配规则'),
        (ContentStatus.RULE_GENERATION_FAILED, '规则生成失败')
    ]
    status_stats = {}
    for status_code, status_name in status_choices:
        count = Content.objects.filter(column_id=column_id, status=status_code).count()
        if count > 0:
            status_stats[status_code] = {
                'label': status_name,
                'count': count
            }
    
    # 获取内容提取规则统计
    from .models import ContentExtractionRule
    
    # 获取该站点的所有内容提取规则
    content_extraction_rules = ContentExtractionRule.objects.filter(site_id=site_id)
    
    # 统计规则使用情况
    rule_stats = {}
    for rule in content_extraction_rules:
        count = Content.objects.filter(
            column_id=column_id,
            content_extraction_rule_id=rule.id
        ).count()
        if count > 0:
            rule_stats[rule.id] = {
                'rule': rule,
                'count': count,
                'name': rule.name or f'规则-{rule.id}'
            }
    
    # 统计有规则和无规则的内容数量
    contents_with_rule = Content.objects.filter(
        column_id=column_id,
        content_extraction_rule_id__isnull=False
    ).count()
    
    contents_without_rule = Content.objects.filter(
        column_id=column_id,
        content_extraction_rule_id__isnull=True
    ).count()
    
    # 排序和分页
    contents = contents.order_by('-publish_time', '-create_time')
    paginator = Paginator(contents, 50)
    page = request.GET.get('page', 1)
    contents_page = paginator.get_page(page)
    
    return render(request, 'column_content_list.html', {
        'site': site,
        'column': column,
        'contents': contents_page,
        'search_query': search_query,
        'status_filter': status_filter,
        'status_stats': status_stats,
        'status_choices': status_choices,
        'total_contents': contents.count(),
        'content_extraction_rules': content_extraction_rules,
        'rule_stats': rule_stats,
        'contents_with_rule': contents_with_rule,
        'contents_without_rule': contents_without_rule
    })


def site_content_list(request, site_id):
    """站点内容列表页面"""
    site = get_object_or_404(Site, id=site_id)
    
    # 获取搜索参数
    search_query = request.GET.get('search', '').strip()
    status_filter = request.GET.get('status', '').strip()
    
    # 获取该站点下所有内容类型的SiteMap
    content_sitemaps = SiteMap.objects.filter(
        site_id=site_id,
        content_type='content'
    ).order_by('url')
    
    # 应用搜索筛选
    if search_query:
        content_sitemaps = content_sitemaps.filter(
            Q(url__icontains=search_query) |
            Q(title__icontains=search_query)
        )
    
    # 获取内容数据并检查关联的Content记录
    contents_data = []
    
    for sitemap in content_sitemaps:
        # 获取关联的Content对象
        content = sitemap.get_content()
        
        # 应用状态筛选
        if status_filter and content:
            if content.status != status_filter:
                continue
        elif status_filter and not content:
            # 如果筛选状态但没有Content记录，跳过
            continue
            
        contents_data.append({
            'sitemap': sitemap,
            'content': content,
            'has_content': content is not None,
            'status': content.status if content else 'no_content',
            'status_label': content.get_status_display() if content else '无内容记录'
        })
    
    # 获取状态统计
    from .enums import ContentStatus
    status_choices = ContentStatus.choices
    status_stats = {}
    
    # 统计各状态的数量
    for status_code, status_name in status_choices:
        count = 0
        for data in contents_data:
            if data['content'] and data['content'].status == status_code:
                count += 1
        if count > 0:
            status_stats[status_code] = {
                'label': status_name,
                'count': count
            }
    
    # 统计无内容记录的数量
    no_content_count = sum(1 for data in contents_data if not data['has_content'])
    if no_content_count > 0:
        status_stats['no_content'] = {
            'label': '无内容记录',
            'count': no_content_count
        }
    
    # 分页处理
    paginator = Paginator(contents_data, 50)
    page = request.GET.get('page', 1)
    contents_page = paginator.get_page(page)
    
    return render(request, 'site_content_list.html', {
        'site': site,
        'contents_data': contents_page,
        'search_query': search_query,
        'status_filter': status_filter,
        'status_stats': status_stats,
        'status_choices': status_choices,
        'total_contents': len(contents_data)
    })