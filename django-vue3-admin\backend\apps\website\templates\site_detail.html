{% extends 'base.html' %}

{% block title %}{{ site.name }} - 站点详情{% endblock %}

{% block breadcrumb %}
<nav aria-label="breadcrumb">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="{% url 'website:site_list' %}">站点列表</a></li>
        <li class="breadcrumb-item active">{{ site.name }}</li>
    </ol>
</nav>
{% endblock %}

{% block content %}
<!-- 站点信息卡片 -->
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-server"></i> 站点信息
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-8">
                <h4>{{ site.name }}</h4>
                <p class="text-muted">{{ site.description|default:"无描述" }}</p>
                
                <div class="row mt-3">
                    <div class="col-md-6">
                        <strong>域名：</strong>
                        <a href="{{ site.start_url }}" target="_blank" class="text-decoration-none">
                            {{ site.domain }}
                            <i class="fas fa-external-link-alt ms-1"></i>
                        </a>
                    </div>
                    <div class="col-md-6">
                        <strong>状态：</strong>
                        {% if site.is_active %}
                            <span class="badge bg-success">活跃</span>
                        {% else %}
                            <span class="badge bg-secondary">停用</span>
                        {% endif %}
                    </div>
                </div>
                
                <div class="row mt-2">
                    <div class="col-md-6">
                        <strong>最大抓取页数：</strong> {{ site.max_crawl_pages }}
                    </div>
                    <div class="col-md-6">
                        <strong>创建时间：</strong> {{ site.created_at|date:"Y-m-d H:i" }}
                    </div>
                </div>
                
                {% if site.parent_site %}
                    <div class="row mt-2">
                        <div class="col-md-12">
                            <strong>父站点：</strong>
                            <a href="{% url 'website:site_detail' site.parent_site.id %}">
                                {{ site.parent_site.name }}
                            </a>
                        </div>
                    </div>
                {% endif %}
                
                <div class="row mt-3">
                    <div class="col-md-12">
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{% url 'website:column_rules_list' site.id %}" class="btn btn-outline-primary btn-sm">
                                <i class="fas fa-cogs"></i> 栏目规则管理
                            </a>
                            <a href="{% url 'website:site_columns_list' site.id %}" class="btn btn-outline-success btn-sm">
                                <i class="fas fa-list"></i> 查看栏目
                            </a>
                            <a href="{% url 'website:site_content_list' site.id %}" class="btn btn-outline-warning btn-sm">
                                <i class="fas fa-file-alt"></i> 查看内容
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <h3>{{ total_sitemaps }}</h3>
                    <p><i class="fas fa-sitemap"></i> 总页面数</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量AI判定操作面板 -->
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-robot"></i> 批量AI内容类型判定
    </div>
    <div class="card-body">
        <div class="row align-items-end">
            <div class="col-md-3">
                <label for="batch-limit" class="form-label">判定数量</label>
                <input type="number" class="form-control" id="batch-limit" value="20" min="1" max="100" 
                       placeholder="输入要判定的数量">
                <small class="form-text text-muted">最多100个</small>
            </div>
            <div class="col-md-6">
                <label class="form-label">筛选条件</label>
                <div class="text-muted">
                    <small>
                        <i class="fas fa-info-circle"></i> 
                        将筛选出未判定的URL（除了"内容"类型），然后进行批量AI分析
                    </small>
                </div>
            </div>
            <div class="col-md-3">
                <button type="button" id="batch-ai-analyze-btn" class="btn btn-success w-100">
                    <i class="fas fa-robot"></i> 开始批量判定
                </button>
            </div>
        </div>
        
        <!-- 进度条和状态显示 -->
        <div id="batch-progress-container" class="mt-3" style="display: none;">
            <div class="d-flex justify-content-between align-items-center mb-2">
                <span class="fw-bold">批量处理进度</span>
                <span id="batch-progress-text">0/0</span>
            </div>
            <div class="progress mb-2">
                <div id="batch-progress-bar" class="progress-bar bg-success" role="progressbar" 
                     style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
            </div>
            <div id="batch-status" class="text-muted">
                <small>准备开始...</small>
            </div>
        </div>
        
        <!-- 结果统计 -->
        <div id="batch-results-container" class="mt-3" style="display: none;">
            <div class="row">
                <div class="col-md-4">
                    <div class="text-center">
                        <h5 class="text-success mb-1" id="success-count">0</h5>
                        <small class="text-muted">成功判定</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h5 class="text-danger mb-1" id="failed-count">0</h5>
                        <small class="text-muted">失败</small>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="text-center">
                        <h5 class="text-info mb-1" id="total-count">0</h5>
                        <small class="text-muted">总计</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- URL匹配规则显示面板 -->
<div id="url-rules-panel" class="card mb-4">
    <div class="card-header">
        <i class="fas fa-code"></i> URL匹配规则
        <button type="button" class="btn btn-primary btn-sm ms-2" id="generate-rules-btn" onclick="generateUrlRules()">
            <i class="fas fa-magic"></i> 生成规则
        </button>
    </div>
    <div class="card-body">
        {% if site.url_matching_rules %}
            <div class="table-responsive">
                <table class="table table-sm table-hover">
                    <thead>
                        <tr>
                            <th style="width: 80px;">序号</th>
                            <th>正则表达式</th>
                            <th style="width: 120px;">内容类型</th>
                            <th style="width: 180px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for rule in site.url_matching_rules %}
                        <tr>
                            <td>{{ forloop.counter }}</td>
                            <td>
                                <code class="text-primary">{{ rule.pattern }}</code>
                            </td>
                            <td>
                                <span class="content-type-badge content-type-{{ rule.content_type }}">
                                    {{ rule.content_type }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-secondary test-rule-btn"
                                            data-pattern="{{ rule.pattern }}"
                                            data-content-type="{{ rule.content_type }}"
                                            title="测试规则">
                                        <i class="fas fa-vial"></i> 测试
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success apply-rule-btn"
                                            data-rule-index="{{ forloop.counter0 }}"
                                            data-pattern="{{ rule.pattern }}"
                                            data-content-type="{{ rule.content_type }}"
                                            title="应用此规则到所有未知类型URL">
                                        <i class="fas fa-play"></i> 应用
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-rule-btn"
                                            data-rule-index="{{ forloop.counter0 }}"
                                            data-pattern="{{ rule.pattern }}"
                                            data-content-type="{{ rule.content_type }}"
                                            title="删除此规则">
                                        <i class="fas fa-trash"></i> 删除
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 规则测试区域 -->
            <div id="rule-test-area" class="mt-3" style="display: none;">
                <div class="row">
                    <div class="col-md-8">
                        <label for="test-url" class="form-label">测试URL：</label>
                        <input type="text" class="form-control" id="test-url" 
                               placeholder="输入要测试的URL">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="button" class="btn btn-primary" onclick="testAllRules()">
                                <i class="fas fa-play"></i> 测试所有规则
                            </button>
                            <button type="button" class="btn btn-success ms-2" id="apply-all-rules-btn">
                                <i class="fas fa-magic"></i> 应用所有规则
                            </button>
                            <button type="button" class="btn btn-info ms-2" onclick="showRuleHelp()">
                                <i class="fas fa-question-circle"></i> 使用说明
                            </button>
                        </div>
                    </div>
                </div>
                <div id="test-results" class="mt-3"></div>
            </div>
        {% else %}
            <div class="text-center py-4">
                <i class="fas fa-exclamation-triangle fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">暂无URL匹配规则</h5>
                <p class="text-muted">请先运行站点初始化流程生成规则</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- 内容类型统计 -->
{% if content_type_stats %}
<div class="card mb-4">
    <div class="card-header">
        <i class="fas fa-chart-pie"></i> 内容类型统计
    </div>
    <div class="card-body">
        <div class="row">
            {% for type_key, type_info in content_type_stats.items %}
                <div class="col-md-3 col-sm-6 mb-3">
                    <div class="content-type-badge content-type-{{ type_key }}">
                        <strong>{{ type_info.label }}</strong>
                        <span class="badge bg-light text-dark ms-2">{{ type_info.count }}</span>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endif %}

<!-- 搜索和筛选 -->
<div class="search-form">
    <form method="get" class="row g-3">
        <div class="col-md-4">
            <label for="search" class="form-label">搜索页面</label>
            <input type="text" class="form-control" id="search" name="search" 
                   value="{{ search_query }}" placeholder="URL或标题关键词">
        </div>
        <div class="col-md-4">
            <label for="content_type" class="form-label">内容类型</label>
            <select class="form-select" id="content_type" name="content_type">
                <option value="">全部类型</option>
                {% for choice in content_type_choices %}
                    <option value="{{ choice.0 }}" {% if choice.0 == content_type %}selected{% endif %}>
                        {{ choice.1 }}
                    </option>
                {% endfor %}
            </select>
        </div>
        <div class="col-md-4">
            <label class="form-label">&nbsp;</label>
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <a href="{% url 'website:site_detail' site.id %}" class="btn btn-outline-secondary">
                    <i class="fas fa-times"></i> 清除
                </a>
            </div>
        </div>
    </form>
</div>

<!-- 页面列表 -->
<div class="card">
    <div class="card-header">
        <i class="fas fa-sitemap"></i> 站点页面列表
        <small class="text-muted">(未知类型优先显示)</small>
        {% if search_query or content_type %}
            <span class="badge bg-secondary ms-2">已筛选</span>
        {% endif %}
        <span class="badge bg-info ms-2">{{ sitemaps.paginator.count }} 个页面</span>
        <div class="float-end">
            <small class="text-muted">
                查看：
                <a href="{% url 'website:site_content_list' site.id %}" class="btn btn-outline-warning btn-sm">
                    <i class="fas fa-file-alt"></i> 内容页面
                </a>
                <a href="{% url 'website:site_columns_list' site.id %}" class="btn btn-outline-success btn-sm ms-1">
                    <i class="fas fa-list"></i> 栏目页面
                </a>
            </small>
        </div>
    </div>
    <div class="card-body p-0">
        {% if sitemaps %}
            <div class="table-responsive">
                <table id="sitemaps-table" class="table table-hover">
                    <thead>
                        <tr>
                            <th style="width: 50px;">ID</th>
                            <th style="width: 35%;">URL</th>
                            <th style="width: 25%;">标题</th>
                            <th style="width: 100px;">内容类型</th>
                            <th style="width: 90px;">创建时间</th>
                            <th style="width: 90px;">最后抓取</th>
                            <th style="width: 120px;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for sitemap in sitemaps %}
                        <tr>
                            <td>{{ sitemap.id }}</td>
                            <td>
                                <a href="{{ sitemap.url }}" target="_blank" class="text-decoration-none" 
                                   title="{{ sitemap.url }}" data-bs-toggle="tooltip">
                                    {{ sitemap.url|truncatechars:45 }}
                                    <i class="fas fa-external-link-alt ms-1 text-muted"></i>
                                </a>
                            </td>
                            <td>
                                <strong>{{ sitemap.title|truncatechars:40 }}</strong>
                            </td>
                            <td>
                                <div class="dropdown">
                                    <button class="btn btn-sm dropdown-toggle content-type-badge 
                                        {% if sitemap.content_type %}content-type-{{ sitemap.content_type }}{% else %}content-type-unknown{% endif %}" 
                                        type="button" 
                                        data-bs-toggle="dropdown" 
                                        title="点击更改内容类型">
                                        {% if sitemap.content_type %}{{ sitemap.get_content_type.label }}{% else %}未判定{% endif %}
                                    </button>
                                    <ul class="dropdown-menu">
                                        {% for choice in content_type_choices %}
                                            {% if choice.0 != sitemap.content_type %}
                                            <li>
                                                <a class="dropdown-item change-content-type-btn" 
                                                   href="#" 
                                                   data-sitemap-id="{{ sitemap.id }}" 
                                                   data-url="{{ sitemap.url }}" 
                                                   data-content-type="{{ choice.0 }}"
                                                   data-label="{{ choice.1 }}">
                                                    <span class="content-type-badge content-type-{{ choice.0 }}">{{ choice.1 }}</span>
                                                </a>
                                            </li>
                                            {% endif %}
                                        {% endfor %}
                                        {% if sitemap.content_type %}
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item change-content-type-btn text-muted" 
                                               href="#" 
                                               data-sitemap-id="{{ sitemap.id }}" 
                                               data-url="{{ sitemap.url }}" 
                                               data-content-type=""
                                               data-label="未判定">
                                                <span class="content-type-badge content-type-unknown">重置为未判定</span>
                                            </a>
                                        </li>
                                        {% endif %}
                                    </ul>
                                </div>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ sitemap.created_at|date:"m-d H:i" }}
                                </small>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ sitemap.last_crawl_time|date:"m-d H:i"|default:"未抓取" }}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'website:sitemap_detail' sitemap.id %}" 
                                       class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> 详情
                                    </a>
                                    <button type="button" 
                                            class="btn btn-sm btn-outline-success ai-analyze-btn"
                                            data-sitemap-id="{{ sitemap.id }}"
                                            data-url="{{ sitemap.url }}"
                                            title="AI内容类型判定">
                                        <i class="fas fa-robot"></i> AI
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">没有找到符合条件的页面</h5>
                {% if search_query or content_type %}
                    <p class="text-muted">请尝试修改搜索条件</p>
                    <a href="{% url 'website:site_detail' site.id %}" class="btn btn-outline-primary">
                        <i class="fas fa-times"></i> 清除筛选
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- 分页 -->
{% if sitemaps.has_other_pages %}
    <nav aria-label="页面分页">
        <ul class="pagination">
            {% if sitemaps.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                        <i class="fas fa-angle-double-left"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ sitemaps.previous_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                        <i class="fas fa-angle-left"></i>
                    </a>
                </li>
            {% endif %}
            
            {% for page_num in sitemaps.paginator.page_range %}
                {% if page_num == sitemaps.number %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% elif page_num > sitemaps.number|add:'-3' and page_num < sitemaps.number|add:'3' %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_num }}{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                            {{ page_num }}
                        </a>
                    </li>
                {% endif %}
            {% endfor %}
            
            {% if sitemaps.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ sitemaps.next_page_number }}{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                        <i class="fas fa-angle-right"></i>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ sitemaps.paginator.num_pages }}{% if search_query %}&search={{ search_query }}{% endif %}{% if content_type %}&content_type={{ content_type }}{% endif %}">
                        <i class="fas fa-angle-double-right"></i>
                    </a>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 启用Bootstrap工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 批量AI分析按钮点击事件
    document.getElementById('batch-ai-analyze-btn').addEventListener('click', function() {
        const limit = parseInt(document.getElementById('batch-limit').value) || 20;
        
        if (limit < 1 || limit > 100) {
            showMessage('error', '判定数量必须在1-100之间');
            return;
        }
        
        startBatchAnalysis(limit);
    });
    
    // 规则测试按钮点击事件
    document.querySelectorAll('.test-rule-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const pattern = this.getAttribute('data-pattern');
            const contentType = this.getAttribute('data-content-type');
            
            // 显示测试区域
            const testArea = document.getElementById('rule-test-area');
            testArea.style.display = 'block';
            
            // 设置当前测试的规则
            document.getElementById('test-url').setAttribute('data-current-pattern', pattern);
            document.getElementById('test-url').setAttribute('data-current-content-type', contentType);
            
            // 滚动到测试区域
            testArea.scrollIntoView({ behavior: 'smooth' });
        });
    });
    
    // 内容类型更改按钮点击事件
    document.querySelectorAll('.change-content-type-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const sitemapId = this.getAttribute('data-sitemap-id');
            const newType = this.getAttribute('data-content-type');
            const newLabel = this.getAttribute('data-label');
            
            // 直接执行类型修改，无需确认
            updateSitemapType(sitemapId, newType, newLabel, this);
        });
    });
    
    // AI分析按钮点击事件
    document.querySelectorAll('.ai-analyze-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const sitemapId = this.getAttribute('data-sitemap-id');
            const url = this.getAttribute('data-url');
            
            // 禁用按钮并显示加载状态
            const originalHtml = this.innerHTML;
            this.disabled = true;
            this.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 分析中';
            
            // 发起AJAX请求
            fetch(`/website/step/update_single_url_content_type/${sitemapId}/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({})
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 更新页面上的内容类型显示
                    const row = this.closest('tr');
                    const contentTypeCell = row.querySelector('td:nth-child(4)');
                    
                    // 更新内容类型标签
                    const newBadgeClass = `content-type-badge content-type-${data.new_content_type}`;
                    contentTypeCell.innerHTML = `<span class="${newBadgeClass}">${data.content_type_label}</span>`;
                    
                    // 显示成功消息
                    showMessage('success', `AI分析完成：${data.content_type_label}`);
                } else {
                    showMessage('error', `分析失败：${data.error}`);
                }
            })
            .catch(error => {
                console.error('请求失败:', error);
                showMessage('error', '请求失败，请稍后重试');
            })
            .finally(() => {
                // 恢复按钮状态
                this.disabled = false;
                this.innerHTML = originalHtml;
            });
        });
    });
    
    // 应用单个规则按钮点击事件
    document.querySelectorAll('.apply-rule-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const ruleIndex = parseInt(this.getAttribute('data-rule-index'));
            const pattern = this.getAttribute('data-pattern');
            const contentType = this.getAttribute('data-content-type');
            
            // 确认对话框
            if (!confirm(`确定要应用此规则吗？\n\n规则：${pattern}\n类型：${contentType}\n\n这将对所有未知类型的URL进行匹配和更新。`)) {
                return;
            }
            
            applyUrlMatchingRules(ruleIndex, `规则 ${ruleIndex + 1}`);
        });
    });
    
    // 应用所有规则按钮点击事件
    document.getElementById('apply-all-rules-btn').addEventListener('click', function() {
        if (!confirm('确定要应用所有URL匹配规则吗？\n\n这将对所有未知类型的URL进行匹配和更新。')) {
            return;
        }
        
        applyUrlMatchingRules(null, '所有规则');
    });
    
    // 删除规则按钮点击事件
    document.querySelectorAll('.delete-rule-btn').forEach(function(button) {
        button.addEventListener('click', function() {
            const ruleIndex = parseInt(this.getAttribute('data-rule-index'));
            const pattern = this.getAttribute('data-pattern');
            const contentType = this.getAttribute('data-content-type');
            
            // 确认对话框
            if (!confirm(`确定要删除此URL匹配规则吗？\n\n规则：${pattern}\n类型：${contentType}\n\n删除后将无法恢复。`)) {
                return;
            }
            
            deleteUrlRule(ruleIndex, pattern);
        });
    });
});

// 获取CSRF Token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 更新sitemap类型函数
function updateSitemapType(sitemapId, newType, newLabel, clickedElement) {
    // 获取相关DOM元素
    const row = clickedElement.closest('tr');
    const contentTypeCell = row.querySelector('td:nth-child(4)');
    const dropdownButton = contentTypeCell.querySelector('button.dropdown-toggle');
    const dropdownMenu = contentTypeCell.querySelector('ul.dropdown-menu');
    
    // 先立即更新UI，提供即时反馈
    const oldClass = dropdownButton.className;
    const oldText = dropdownButton.textContent;
    
    // 更新按钮样式和文本
    const newBadgeClass = `btn btn-sm dropdown-toggle content-type-badge content-type-${newType || 'unknown'}`;
    dropdownButton.className = newBadgeClass;
    dropdownButton.textContent = newLabel || '未判定';
    
    // 发起AJAX请求更新类型
    fetch(`/website/api/update_sitemap_type/${sitemapId}/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            content_type: newType
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新下拉菜单选项，重新构建菜单内容
            updateDropdownMenu(dropdownMenu, sitemapId, row.querySelector('td:nth-child(2) a').href, data.new_content_type);
            
            // 简短的成功提示
            showMessage('success', `已更改为：${data.content_type_label || '未判定'}`, 2000);
        } else {
            // 失败时恢复原状
            dropdownButton.className = oldClass;
            dropdownButton.textContent = oldText;
            showMessage('error', `修改失败：${data.error}`);
        }
    })
    .catch(error => {
        // 失败时恢复原状
        dropdownButton.className = oldClass;
        dropdownButton.textContent = oldText;
        console.error('请求失败:', error);
        showMessage('error', '请求失败，请稍后重试');
    });
}

// 更新下拉菜单选项
function updateDropdownMenu(dropdownMenu, sitemapId, url, currentType) {
    // 获取所有可用的内容类型选项
    const contentTypeChoices = {{ content_type_choices_json|safe }};
    
    let menuHtml = '';
    contentTypeChoices.forEach(choice => {
        if (choice[0] !== currentType) {
            menuHtml += `
                <li>
                    <a class="dropdown-item change-content-type-btn" 
                       href="#" 
                       data-sitemap-id="${sitemapId}" 
                       data-url="${url}" 
                       data-content-type="${choice[0]}"
                       data-label="${choice[1]}">
                        <span class="content-type-badge content-type-${choice[0]}">${choice[1]}</span>
                    </a>
                </li>
            `;
        }
    });
    
    if (currentType) {
        menuHtml += `
            <li><hr class="dropdown-divider"></li>
            <li>
                <a class="dropdown-item change-content-type-btn text-muted" 
                   href="#" 
                   data-sitemap-id="${sitemapId}" 
                   data-url="${url}" 
                   data-content-type=""
                   data-label="未判定">
                    <span class="content-type-badge content-type-unknown">重置为未判定</span>
                </a>
            </li>
        `;
    }
    
    dropdownMenu.innerHTML = menuHtml;
    
    // 重新绑定事件监听器
    dropdownMenu.querySelectorAll('.change-content-type-btn').forEach(function(button) {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            
            const sitemapId = this.getAttribute('data-sitemap-id');
            const newType = this.getAttribute('data-content-type');
            const newLabel = this.getAttribute('data-label');
            
            updateSitemapType(sitemapId, newType, newLabel, this);
        });
    });
}

// 显示消息提示
function showMessage(type, message, duration = 5000) {
    let alertClass = 'alert-primary';
    if (type === 'success') alertClass = 'alert-success';
    else if (type === 'error') alertClass = 'alert-danger';
    else if (type === 'warning') alertClass = 'alert-warning';
    else if (type === 'info') alertClass = 'alert-info';
    
    // 检查消息是否包含HTML标签
    const isHtml = /<[a-z][\s\S]*>/i.test(message);
    const messageContent = isHtml ? message : message.replace(/\n/g, '<br>');
    
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
            ${messageContent}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    // 在页面顶部显示消息
    const container = document.querySelector('.container.main-content');
    const breadcrumb = container.querySelector('nav[aria-label="breadcrumb"]');
    if (breadcrumb) {
        breadcrumb.insertAdjacentHTML('afterend', alertHtml);
    } else {
        container.insertAdjacentHTML('afterbegin', alertHtml);
    }
    
    // 自定义时长后自动隐藏
    setTimeout(() => {
        const alert = container.querySelector('.alert');
        if (alert) {
            alert.remove();
        }
    }, duration);
}

// 批量AI分析函数
function startBatchAnalysis(limit) {
    const batchBtn = document.getElementById('batch-ai-analyze-btn');
    const progressContainer = document.getElementById('batch-progress-container');
    const resultsContainer = document.getElementById('batch-results-container');
    const progressBar = document.getElementById('batch-progress-bar');
    const progressText = document.getElementById('batch-progress-text');
    const batchStatus = document.getElementById('batch-status');
    
    // 禁用按钮并显示加载状态
    const originalBtnHtml = batchBtn.innerHTML;
    batchBtn.disabled = true;
    batchBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 处理中...';
    
    // 显示进度容器
    progressContainer.style.display = 'block';
    resultsContainer.style.display = 'block';
    
    // 重置进度和结果
    progressBar.style.width = '0%';
    progressText.textContent = '0/0';
    batchStatus.innerHTML = '<small>正在获取待处理URL列表...</small>';
    document.getElementById('success-count').textContent = '0';
    document.getElementById('failed-count').textContent = '0';
    document.getElementById('total-count').textContent = '0';
    
    // 调用现有的批量AI判定API
    const siteId = {{ site.id }};
    
    fetch('/website/step/update_content_type_by_ai/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            site_id: siteId,
            limit: limit
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 更新结果统计
            document.getElementById('success-count').textContent = data.successful_count || 0;
            document.getElementById('failed-count').textContent = data.failed_count || 0;
            document.getElementById('total-count').textContent = data.total_processed || 0;
            
            // 更新进度条到100%
            progressBar.style.width = '100%';
            progressText.textContent = `${data.total_processed}/${data.total_processed}`;
            
            if (data.interrupted) {
                batchStatus.innerHTML = '<small class="text-warning">处理被中断，已完成部分URL的分析</small>';
                showMessage('warning', `批量分析被中断，已完成 ${data.total_processed} 个URL的分析`);
            } else {
                batchStatus.innerHTML = '<small class="text-success">批量分析完成！</small>';
                showMessage('success', `批量分析完成，成功分析 ${data.successful_count} 个URL`);
            }
            
            // 刷新页面以显示更新后的结果
            setTimeout(() => {
                window.location.reload();
            }, 2000);
            
        } else {
            batchStatus.innerHTML = '<small class="text-danger">批量分析失败</small>';
            showMessage('error', `批量分析失败：${data.error}`);
        }
    })
    .catch(error => {
        console.error('批量分析请求失败:', error);
        batchStatus.innerHTML = '<small class="text-danger">请求失败</small>';
        showMessage('error', '请求失败，请稍后重试');
    })
    .finally(() => {
        // 恢复按钮状态
        batchBtn.disabled = false;
        batchBtn.innerHTML = originalBtnHtml;
    });
}


// 测试所有规则函数
function testAllRules() {
    const testUrl = document.getElementById('test-url').value.trim();
    const resultsDiv = document.getElementById('test-results');
    
    if (!testUrl) {
        showMessage('error', '请输入要测试的URL');
        return;
    }
    
    // 获取所有规则
    const rules = {{ site.url_matching_rules|safe }};
    
    let results = '<div class="alert alert-info"><strong>测试结果：</strong></div>';
    let matchFound = false;
    
    results += '<div class="table-responsive"><table class="table table-sm">';
    results += '<thead><tr><th>规则</th><th>内容类型</th><th>匹配结果</th></tr></thead><tbody>';
    
    rules.forEach((rule, index) => {
        try {
            const regex = new RegExp(rule.pattern);
            const isMatch = regex.test(testUrl);
            
            results += `<tr class="${isMatch ? 'table-success' : 'table-light'}">`;
            results += `<td><code>${rule.pattern}</code></td>`;
            results += `<td><span class="content-type-badge content-type-${rule.content_type}">${rule.content_type}</span></td>`;
            results += `<td>`;
            
            if (isMatch) {
                results += '<i class="fas fa-check text-success"></i> 匹配';
                matchFound = true;
            } else {
                results += '<i class="fas fa-times text-muted"></i> 不匹配';
            }
            
            results += '</td></tr>';
        } catch (e) {
            results += `<tr class="table-warning">`;
            results += `<td><code>${rule.pattern}</code></td>`;
            results += `<td><span class="content-type-badge content-type-${rule.content_type}">${rule.content_type}</span></td>`;
            results += `<td><i class="fas fa-exclamation-triangle text-warning"></i> 正则表达式错误</td>`;
            results += '</tr>';
        }
    });
    
    results += '</tbody></table></div>';
    
    if (!matchFound) {
        results += '<div class="alert alert-warning mt-2">该URL没有匹配任何规则</div>';
    } else {
        results += '<div class="alert alert-success mt-2">找到匹配的规则！</div>';
    }
    
    resultsDiv.innerHTML = results;
}

// 应用URL匹配规则函数
function applyUrlMatchingRules(ruleIndex, ruleDescription) {
    const siteId = {{ site.id }};
    
    // 准备请求数据
    const requestData = {
        site_id: siteId
    };
    
    if (ruleIndex !== null) {
        requestData.rule_index = ruleIndex;
    }
    
    // 显示加载提示
    showMessage('info', `正在应用${ruleDescription}，请稍候...`);
    
    // 发起AJAX请求
    fetch('/website/api/apply_url_matching_rules/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // 显示成功消息
            let message = `${data.message}：处理了 ${data.processed_count} 个URL，更新了 ${data.updated_count} 个。`;
            
            if (data.update_details && data.update_details.length > 0) {
                message += '\n\n更新详情：';
                data.update_details.forEach(detail => {
                    message += `\n• ${detail.url} (${detail.old_type} → ${detail.new_type})`;
                });
                
                if (data.has_more_details) {
                    message += '\n... 还有更多更新';
                }
            }
            
            showMessage('success', message);
            
            // 如果有更新，刷新页面显示最新结果
            if (data.updated_count > 0) {
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            }
        } else {
            showMessage('error', `应用规则失败：${data.error}`);
        }
    })
    .catch(error => {
        console.error('应用规则请求失败:', error);
        showMessage('error', '请求失败，请稍后重试');
    });
}

// 删除URL规则函数
function deleteUrlRule(ruleIndex, pattern) {
    const siteId = {{ site.id }};
    
    // 显示加载提示
    showMessage('info', `正在删除规则: ${pattern}...`);
    
    // 发起AJAX请求
    fetch('/website/api/delete_url_rule/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            site_id: siteId,
            rule_index: ruleIndex
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showMessage('success', `规则删除成功: ${pattern}`);
            
            // 刷新页面以显示更新后的规则列表
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            showMessage('error', `删除规则失败：${data.error}`);
        }
    })
    .catch(error => {
        console.error('删除规则请求失败:', error);
        showMessage('error', '请求失败，请稍后重试');
    });
}

// 显示规则使用说明
function showRuleHelp() {
    const helpContent = `
    <div class="alert alert-info">
        <h5><i class="fas fa-info-circle"></i> URL匹配规则使用说明</h5>
        <h6>🔄 新的工作流程：</h6>
        <ol>
            <li><strong>手动初筛</strong>：在页面列表中手动将一些明确的URL标记为正确类型（内容、栏目等）</li>
            <li><strong>生成规则</strong>：点击"生成URL匹配规则"，AI会参考您的手动分类生成更准确的规则</li>
            <li><strong>测试验证</strong>：在规则列表中测试生成的规则是否正确匹配</li>
            <li><strong>应用规则</strong>：确认规则无误后，点击"应用"按钮将规则应用到所有未知类型的URL</li>
            <li><strong>清理规则</strong>：如有无效规则，可点击"删除"按钮清理</li>
        </ol>
        
        <h6>💡 优化建议：</h6>
        <ul>
            <li>手动标记的样本越多，AI生成的规则越准确</li>
            <li>优先标记具有典型特征的URL（如明显的栏目页、内容页）</li>
            <li>每次生成规则前，建议先检查现有规则是否需要删除</li>
        </ul>
    </div>
    `;
    
    showMessage('info', helpContent);
}

// 生成URL匹配规则函数
function generateUrlRules() {
    const siteId = {{ site.id }};
    const generateBtn = document.getElementById('generate-rules-btn');
    
    // 收集当前页面上所有已手动判定的类型数据
    const manualClassifiedData = [];
    const tableRows = document.querySelectorAll('#sitemaps-table tbody tr');
    
    tableRows.forEach(row => {
        const sitemapIdCell = row.querySelector('td:first-child');
        const urlCell = row.querySelector('td:nth-child(2) a');
        const contentTypeCell = row.querySelector('td:nth-child(4) span');
        
        if (sitemapIdCell && urlCell && contentTypeCell) {
            const sitemapId = parseInt(sitemapIdCell.textContent.trim());
            const url = urlCell.getAttribute('href');
            const contentTypeClasses = contentTypeCell.className;
            
            // 提取content_type (从class="content-type-badge content-type-xxx"中获取)
            const contentTypeMatch = contentTypeClasses.match(/content-type-(\w+)/);
            const contentType = contentTypeMatch ? contentTypeMatch[1] : null;
            
            if (sitemapId && url && contentType && contentType !== 'unknown') {
                manualClassifiedData.push({
                    sitemap_id: sitemapId,
                    url: url,
                    content_type: contentType
                });
            }
        }
    });
    
    // 确认对话框
    const confirmMessage = `确定要为此站点生成URL匹配规则吗？\n\n这将使用AI分析站点的所有URL，生成内容分类规则。` + 
                          (manualClassifiedData.length > 0 ? `\n\n发现 ${manualClassifiedData.length} 个已手动判定的页面，这些数据将帮助AI生成更准确的规则。` : '');
    
    if (!confirm(confirmMessage)) {
        return;
    }
    
    // 禁用按钮并显示加载状态
    if (generateBtn) {
        const originalHtml = generateBtn.innerHTML;
        generateBtn.disabled = true;
        generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 生成中...';
        
        // 显示进度提示
        showMessage('info', '正在分析站点URL并生成匹配规则，请稍候...');
        
        // 发起AJAX请求调用生成规则接口
        fetch('/website/step/generate_rules_to_sitemap/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify({
                site_id: siteId,
                manual_classified_data: manualClassifiedData
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 显示成功消息
                let message = `URL匹配规则生成成功！\n\n`;
                message += `站点：${data.site_name}\n`;
                message += `生成规则：${data.generated_rules ? data.generated_rules.length : 0} 条\n`;
                message += `总URL数：${data.total_urls || 0} 个\n`;
                message += `处理耗时：${(data.processing_time || 0).toFixed(2)} 秒`;
                
                // 显示手动分类数据使用情况
                if (data.manual_data_count > 0) {
                    message += `\n\n使用了 ${data.manual_data_count} 个手动分类样本来提升AI准确性`;
                }
                
                message += `\n\n⚠️ 请在规则列表中测试规则正确性，确认无误后手动应用`;
                
                showMessage('success', message);
                
                // 2秒后刷新页面显示新生成的规则
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
                
            } else {
                showMessage('error', `规则生成失败：${data.error}`);
                
                // 恢复按钮状态
                generateBtn.disabled = false;
                generateBtn.innerHTML = originalHtml;
            }
        })
        .catch(error => {
            console.error('生成规则请求失败:', error);
            showMessage('error', '请求失败，请稍后重试');
            
            // 恢复按钮状态
            generateBtn.disabled = false;
            generateBtn.innerHTML = originalHtml;
        });
    }
}
</script>
{% endblock %} 